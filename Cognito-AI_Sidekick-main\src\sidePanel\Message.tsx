import '../content/index.css';
import type { FC } from 'react';
import { useState, useEffect } from 'react';
import Markdown from 'react-markdown';
import { FiCheck, FiX } from './icons';
import { Textarea } from "@/components/ui/textarea";

import { Button } from "@/components/ui/button";
import { cn } from "@/src/background/util";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import remarkGfm from 'remark-gfm';
import remarkSupersub from 'remark-supersub';

import { useConfig } from './ConfigContext';
import { markdownComponents, Pre } from '@/components/MarkdownComponents';
import type { MessageTurn } from './ChatHistory';

const ThinkingBlock = ({ content }: { content: string }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-2">
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
        <CollapsibleTrigger asChild>
          <Button
            variant="outline" 
            size="sm"
            className={cn(
              "mb-1", 
              "border-foreground text-foreground hover:text-accent-foreground" 
            )}
          >
            {isOpen ? 'Hide Thoughts' : 'Show Thoughts'}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div
            className={cn(
              "p-3 rounded-md border border-dashed",
              "bg-muted",
              "border-muted-foreground",
              "text-muted-foreground" 
            )}
          >
            <div className="markdown-body">
              <Markdown
                remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkSupersub]}
                components={messageMarkdownComponents}
              >
                {content}
              </Markdown>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

const messageMarkdownComponents = {
  ...markdownComponents,
  pre: (props: React.ComponentPropsWithoutRef<typeof Pre>) => <Pre {...props} buttonVariant="copy-button" />,
}

interface MessageProps {
  turn: MessageTurn;
  index: number;
  isEditing: boolean;
  editText: string;
  onStartEdit: (index: number, currentContent: string) => void;
  onSetEditText: (text: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
}

export const EditableMessage: FC<MessageProps> = ({
  turn, index, isEditing, editText, onStartEdit, onSetEditText, onSaveEdit, onCancelEdit
}) => {
  const { config } = useConfig();
  const contentToRender = turn.rawContent || '';
  const parts = contentToRender.split(/(<think>[\s\S]*?<\/think>)/g).filter(part => part && part.trim() !== '');
  const thinkRegex = /<think>([\s\S]*?)<\/think>/;

  useEffect(() => {
    if (!isEditing) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        onCancelEdit();
      } else if (event.key === 'Enter' && !event.shiftKey && !event.altKey && !event.metaKey) {
        if (editText.trim()) {
          event.preventDefault();
          event.stopPropagation();
          onSaveEdit();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown, true);
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [isEditing, onCancelEdit, onSaveEdit, editText]);

  return (
    <div
      className={cn(
        "text-base relative",
        "max-w-[85%] px-4 py-3",
        turn.role === 'assistant'
          ? 'text-foreground text-left'
          : 'text-foreground/90 text-right',
        'chatMessage', isEditing ? 'editing' : '',
        config && typeof config.fontSize === 'number' && config.fontSize <= 15 ? 'font-semibold' : ''
      )}
      onDoubleClick={() => {
        if (!isEditing) {
          onStartEdit(index, turn.rawContent);
        }
      }}
    >
      {isEditing ? (
        <div className="flex flex-col space-y-2 items-stretch w-full p-1">
          <Textarea
            autosize 
            value={editText}
            onChange={(e) => onSetEditText(e.target.value)}
            placeholder="Edit your message..."
            className={cn(
              "w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground",
              "border-input",
              "text-foreground",
              "hover:border-primary focus-visible:border-primary focus-visible:ring-0",
              "min-h-[60px]"
            )}
            minRows={3}
            autoFocus
          />
          <div className="flex font-mono justify-end space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onSaveEdit}
              title="Save changes"
            >
              <FiCheck className="h-4 w-4 mr-1" /> Save
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onCancelEdit}
              title="Discard changes"
            >
              <FiX className="h-4 w-4 mr-1" /> Exit
            </Button>
          </div>
        </div>
      ) : (
        <div className={cn(
          "message-markdown markdown-body relative z-[1] text-foreground",
          turn.role === 'user' ? 'text-right [&_*]:text-right' : 'text-left [&_*]:text-left'
        )}>
          <div className={cn(
            "text-apple-caption1 font-medium mb-2",
            turn.role === 'assistant'
              ? 'text-muted-foreground text-left'
              : 'text-muted-foreground/80 text-right'
          )}>
            {turn.role === 'assistant' ? 'Assistant' : 'You'}
          </div>

          {turn.role === 'assistant' && turn.webDisplayContent && (
            <div className="message-prefix">
              <Markdown remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkSupersub]} components={messageMarkdownComponents}>
              </Markdown>
            </div>
          )}
          {parts.map((part, partIndex) => {
            const match = part.match(thinkRegex);
            if (match && match[1]) {
              return <ThinkingBlock key={`think_${partIndex}`} content={match[1]} />;
            } else {
              return (
                <div key={`content_${partIndex}`} className={cn(
                  "message-content",
                  turn.role === 'user' ? 'text-right [&_*]:text-right' : 'text-left [&_*]:text-left'
                )}>
                <Markdown
                  remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkSupersub]}
                  components={messageMarkdownComponents}
                >{part}</Markdown>
                </div>
              );
            }
          })}
        </div>
      )}
    </div>
  );
};